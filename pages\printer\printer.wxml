<!--printer.wxml-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
    <view class="navbar-content" style="height: {{navBarHeight}}px;">
      <image class="navbar-logo" src="/static/images/logo.png" mode="aspectFit"></image>
      <view class="navbar-title">标签打印</view>
    </view>
  </view>

  <!-- 隐藏的Canvas - 精臣B1打印机版本 -->
  <view class="hidden-canvas">
    <!-- 原有Canvas保留用于兼容 -->
    <canvas width='{{templateWidth*pixelRatio}}' height='{{templateHeight*pixelRatio}}' style="width:{{templateWidth}}px;height:{{templateHeight}}px" canvas-id="Canvas" id="Canvas"></canvas>
    <canvas type="2d" style="width:{{barCodeWidth}}px;height:{{barCodeHeight}}px" class="ls-barcode-canvas" canvas-id="barCodels" id="barCodels"></canvas>
    <canvas style="width:{{qrCodeWidth}}px; height: {{qrCodeHeight}}px;" canvas-id="qrCode"></canvas>

    <!-- 精臣SDK专用Canvas -->
    <canvas canvas-id="previewCanvas" style="width: 320px; height: 240px;"></canvas>
    <canvas canvas-id="printCanvas" style="width: 320px; height: 240px;"></canvas>
  </view>

  <!-- 页面内容区域 -->
  <view class="page-content">
      <!-- 打印机状态栏 -->
    <view class="printer-status {{printerStatus}} {{isScanning ? 'scanning' : ''}} {{isConnecting ? 'connecting' : ''}}">
      <view wx:if="{{isScanning}}" class="status-text">
        <text class="status-icon">🔍</text>
        <text>正在搜索打印机...</text>
      </view>
      <view wx:elif="{{isConnecting || printerStatus === 'connecting'}}" class="status-text">
        <text class="status-icon">🔗</text>
        <text>正在连接打印机...</text>
      </view>
      <view wx:elif="{{printerStatus === 'disconnected'}}" class="status-text">
        <text class="status-icon">⚠️</text>
        <text>待连接打印机</text>
      </view>
      <view wx:elif="{{printerStatus === 'printing'}}" class="status-text">
        <text class="status-icon">🖨️</text>
        <text>正在打印中...</text>
        <view class="stop-print-btn" bindtap="stopPrint">停止</view>
      </view>
      <view wx:elif="{{printerStatus === 'error'}}" class="status-text">
        <text class="status-icon">❌</text>
        <view class="error-info">
          <text class="error-message">{{printerErrorMessage}}</text>
          <text wx:if="{{printerErrorCode}}" class="error-code">(错误码: {{printerErrorCode}})</text>
        </view>
      </view>
      <view wx:elif="{{printerStatus === 'connected' && printerErrorMessage}}" class="status-text">
        <text class="status-icon">⚠️</text>
        <view class="error-info">
          <text class="warning-message">已连接【{{printerDeviceSn}}】- {{printerErrorMessage}}</text>
          <text wx:if="{{printerErrorCode}}" class="error-code">(错误码: {{printerErrorCode}})</text>
        </view>
        <view class="status-actions">
          <!-- <view class="refresh-material-btn" bindtap="refreshMaterialInfo">
            <text>刷新</text>
            <text>耗材</text>
          </view> -->
          <view class="change-device-btn" bindtap="changeDevice">
            <text>更换</text>
            <text>设备</text>
          </view>
        </view>
      </view>
      <view wx:else class="status-text">
        <text class="status-icon">✅</text>
        <text>已连接打印机【{{printerDeviceSn}}】</text>
        <view class="status-actions">
          <!-- <view class="refresh-material-btn" bindtap="refreshMaterialInfo">
            <text>刷新</text>
            <text>耗材</text>
          </view> -->
          <view class="change-device-btn" bindtap="changeDevice">
            <text>更换</text>
            <text>设备</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 标签规格列表 -->
    <view class="template-section">
      <view class="section-title">标签规格</view>
      <scroll-view class="template-list" scroll-x="true">
        <view wx:for="{{templates}}" wx:key="index" 
              class="template-item {{selectedTemplateIndex === index ? 'selected' : ''}}"
              data-index="{{index}}" 
              bindtap="selectTemplate">
          <view class="template-preview">
            <image src="{{item.PreviewPath}}" mode="aspectFit" class="template-image"></image>
          </view>
          <view class="template-name">{{item.TemplateName}}</view>
        </view>
      </scroll-view>
    </view>

    <!-- 预览图 -->
    <view class="preview-section">
      <view class="section-title">标签预览</view>
      <view class="preview-container">
        <image wx:if="{{previewImagePath}}" src="{{previewImagePath}}" mode="aspectFit" class="preview-image"></image>
        <view wx:else class="preview-placeholder">预览图生成中...</view>
      </view>
    </view>

    <!-- 标签内容输入 -->
    <view class="content-section">
      <view class="section-title">标签内容</view>
      <view class="content-form">
        <view class="form-item">
          <text class="form-label">品名：</text>
          <input class="form-input" 
                type="text" 
                value="{{labelContent.productName}}" 
                bindinput="onProductNameInput" 
                placeholder="请输入品名" 
                maxlength="20" />
        </view>
        <view class="form-item">
          <text class="form-label">操作人：</text>
          <input class="form-input" 
                type="text" 
                value="{{labelContent.operator}}" 
                bindinput="onOperatorInput" 
                placeholder="请输入操作人" 
                maxlength="20" />
        </view>
        <view class="form-item">
          <text class="form-label">日期：</text>
          <picker class="form-picker" 
                  mode="date" 
                  value="{{labelContent.date}}" 
                  bindchange="onDateChange">
            <view class="picker-text">{{labelContent.date || '请选择日期'}}</view>
          </picker>
        </view>
        <view class="form-item">
          <text class="form-label">打印份数：</text>
          <view class="copies-control">
            <view class="copies-btn" bindtap="decreaseCopies">-</view>
            <input class="copies-input"
                  type="number"
                  value="{{labelContent.copies}}"
                  bindinput="onCopiesInput"
                  placeholder="1" />
            <view class="copies-btn" bindtap="increaseCopies">+</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部菜单栏 -->
    <view class="bottom-menu">
      <view class="menu-item" bindtap="openOnlineStore">
        <view class="menu-icon">🛒</view>
        <text class="menu-text">在线商城</text>
      </view>
      
      <view class="menu-item main-action {{isPrinting ? 'printing' : ''}} {{isConnecting || isScanning ? 'connecting' : ''}}" bindtap="onPrintAction">
        <view class="menu-icon">
          {{isPrinting ? '⏹️' : (isScanning ? '🔍' : (isConnecting ? '🔗' : (printerStatus === 'connected' ? '🖨️' : '🔗')))}}
        </view>
        <text class="menu-text-center">
          {{isPrinting ? '停止打印' : (isScanning ? '搜索中' : (isConnecting ? '连接中' : (printerStatus === 'connected' ? '开始打印' : '连接设备')))}}
        </text>
      </view>
      
      <view class="menu-item" bindtap="showContact">
        <view class="menu-icon">📞</view>
        <text class="menu-text">联系我们</text>
      </view>
    </view>

    <!-- 设备选择对话框 -->
    <view class="modal" wx:if="{{showDeviceSelector}}">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">选择打印机</text>
          <view wx:if="{{isScanning}}" class="scanning-status">🔍 搜索中...</view>
          <view class="modal-close" bindtap="closeDeviceSelector">×</view>
        </view>
        <view class="device-list">
          <view wx:if="{{blueList.length === 0 && !isScanning}}" class="no-device-tip">
            <text>未找到设备，请确保设备已开启并重新搜索</text>
          </view>
          <view wx:if="{{blueList.length === 0 && isScanning}}" class="scanning-tip">
            <text>正在搜索设备...</text>
          </view>
          <view wx:for="{{blueList}}" wx:key="index"
                class="device-item {{(item.name || item.deviceId) === printerDeviceSn ? 'current-device' : ''}}"
                data-index="{{index}}"
                bindtap="selectDevice">
            <view class="device-info">
              <text class="device-name">{{item.name || item.deviceId}}</text>
              <text wx:if="{{(item.name || item.deviceId) === printerDeviceSn}}" class="device-status">已连接</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 联系我们弹窗 -->
    <view class="modal" wx:if="{{showContactModal}}">
      <view class="modal-content contact-modal">
        <view class="modal-header">
          <text class="modal-title">联系我们</text>
          <view class="modal-close" bindtap="closeContact">×</view>
        </view>
        <view class="contact-info">
          <view class="contact-item" bindtap="copyContact" data-text="400-622-9388">
            <text class="contact-label">服务热线：</text>
            <text class="contact-value">400-622-9388</text>
          </view>
          <view class="contact-item" bindtap="copyContact" data-text="020-89577250">
            <text class="contact-label">服务传真：</text>
            <text class="contact-value">020-89577250</text>
          </view>
          <view class="contact-item" bindtap="copyContact" data-text="<EMAIL>">
            <text class="contact-label">服务邮箱：</text>
            <text class="contact-value"><EMAIL></text>
          </view>
          <view class="contact-item">
            <text class="contact-label">地址：</text>
            <text class="contact-value">广州市海珠区泉塘路 2 号之三（浩诚商务中心）605 惠而信</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">官方微信公众号：</text>
            <text class="contact-value">惠而信</text>
          </view>
        </view>
      </view>
    </view>

  </view> <!-- 页面内容区域结束 -->
</view>
