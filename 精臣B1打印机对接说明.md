# 精臣B1打印机对接说明

## 概述
本项目基于原有的printer页面功能，成功对接了精臣B1打印机SDK（JCAPI.js），替代了原有的SUPVANAPIT50PRO SDK。

## 主要功能特性

### 1. SDK替换
- ✅ 将SUPVANAPIT50PRO SDK替换为精臣JCAPI SDK
- ✅ 更新所有蓝牙连接、扫描、打印相关方法
- ✅ 保持原有功能接口的兼容性

### 2. 错误码适配
- ✅ 扩展printerStatus.js支持精臣B1错误码
- ✅ 添加0xff00-0xffff范围的精臣专用错误码
- ✅ 提供用户友好的错误提示信息

### 3. 模板转换功能
- ✅ 创建templateConverter.js工具
- ✅ 支持200dpi精度转换（1mm = 8px）
- ✅ 自动转换templates.js配置为精臣绘制格式
- ✅ 支持文本、条码、二维码、线条、矩形、图片等元素

### 4. 蓝牙连接限制
- ✅ 基于printerSn.js配置文件
- ✅ 只允许连接指定序列号的设备（H613070302）
- ✅ 自动过滤未授权设备

### 5. 耗材兼容性适配
- ✅ B1设备不支持getPageInfos，使用模拟兼容数据
- ✅ 默认设置耗材规格为兼容状态
- ✅ 避免因耗材检查导致的打印阻塞

### 6. Demo入口
- ✅ 在index页面添加"精臣B1打印机Demo"按钮
- ✅ 提供功能说明和设备限制提示
- ✅ 一键跳转到printer页面

## 技术实现

### 核心文件修改
1. **pages/printer/printer.js** - 主要功能实现
   - 替换SDK导入和方法调用
   - 实现精臣B1的连接、扫描、打印逻辑
   - 使用模板转换工具优化绘制流程

2. **config/printerStatus.js** - 错误码扩展
   - 添加精臣B1专用错误码映射
   - 扩展错误分类和级别定义
   - 提供用户友好的错误提示

3. **config/printerSn.js** - 设备授权配置
   - 定义允许连接的设备列表
   - 提供设备验证方法

4. **utils/templateConverter.js** - 模板转换工具
   - mm到px的精度转换
   - 模板配置到精臣绘制参数的转换
   - 支持多种绘制元素类型

### 精度转换
- **B1打印机精度**: 200dpi
- **转换公式**: 1mm = 8px
- **支持单位**: 自动处理mm到px的转换

### 设备限制
- **允许设备**: H613070302（可在config/printerSn.js中配置）
- **连接验证**: 自动过滤未授权设备
- **安全性**: 防止连接到未授权的打印机设备

## 使用说明

### 启动Demo
1. 在index页面点击"精臣B1打印机Demo"按钮
2. 确认功能说明后进入printer页面
3. 开始使用精臣B1打印机功能

### 连接设备
1. 确保精臣B1打印机已开启
2. 设备序列号必须为H613070302
3. 点击连接按钮自动扫描并连接

### 打印标签
1. 选择合适的模板（40x30mm或50x30mm）
2. 填写标签内容（品名、操作人、日期）
3. 设置打印份数
4. 点击打印按钮执行打印

### 预览功能
- 支持实时预览标签效果
- 自动应用200dpi精度转换
- 显示最终打印效果

## 注意事项

### B1设备特性
- ❌ 不支持gap间隙设置
- ❌ 不支持getPageInfos获取耗材信息
- ✅ 支持200dpi高精度打印
- ✅ 支持蓝牙连接和控制

### 兼容性
- 保持与原有printer页面的功能兼容
- 模板配置格式保持不变
- 错误处理机制向下兼容

### 安全性
- 设备连接白名单机制
- 错误码统一处理
- 用户友好的提示信息

## 开发者说明

### 扩展设备支持
如需添加更多允许的设备，请修改`config/printerSn.js`文件：

```javascript
const allowedPrinterSns = [
  {
    sn: 'H613070302',
    name: '精臣B1打印机-01',
    model: 'B1',
    description: '精臣B1标签打印机，200dpi精度'
  },
  // 添加新设备...
];
```

### 自定义模板
模板配置保持原有格式，系统会自动转换为精臣B1格式。

### 错误处理
所有精臣B1错误码都已映射到printerStatus.js中，支持统一的错误处理流程。

---

**开发完成时间**: 2025年1月
**技术栈**: 微信小程序 + 精臣JCAPI SDK
**兼容设备**: 精臣B1标签打印机
